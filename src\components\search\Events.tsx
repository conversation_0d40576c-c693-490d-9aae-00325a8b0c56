import React, { useEffect, useState, useRef, useCallback } from "react";
import { FilterSearchManager, EventSearchBy } from "@/services/filtersServices";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import EmptyState from "@/components/EmptyState";
import { themes } from "../../../theme";
import Link from "next/link";

const Events = ({ searchFor, searchBy, search, SEARCH_FOR, SEARCH_BY_MAP, border, onClose, filters }: any) => {
  const [allEvents, setAllEvents] = useState<any[]>([]); // full list from backend
  const [visibleEvents, setVisibleEvents] = useState<any[]>([]); // paginated slice
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const PAGE_SIZE = 50;

  useEffect(() => {
    // console.log(filters);
    
    let filterBy;
    if (searchBy === 'Description') {
      filterBy = EventSearchBy.DESCRIPTION;
    } else if (searchBy === 'Name' || searchBy === 'Title' || searchBy === 'Category') {
      filterBy = EventSearchBy.NAME;
    } else {
      setAllEvents([]);
      setVisibleEvents([]);
      setHasMore(false);
      return;
    }
    setLoading(true);
    setError(null);
    FilterSearchManager.getInstance().GetEventByFilters({
      payload: {
        filterBy,
        searchTerm: search,
        limit: 5000, // get all possible results (adjust if needed)
        filters, // <-- pass filters here
      }
    })
      .then((resp) => {
        const filtered = Array.isArray(resp.events) ? resp.events : [];
        setAllEvents(filtered);
        setVisibleEvents(filtered.slice(0, PAGE_SIZE));
        setHasMore(filtered.length > PAGE_SIZE);
      })
      .catch((error) => {
        setError(error && error.message ? error.message : 'Error fetching events');
        setAllEvents([]);
        setVisibleEvents([]);
        setHasMore(false);
      })
      .finally(() => setLoading(false));
  }, [search, searchBy, filters]);

  // Scroll handler for frontend-only pagination
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || loading || !hasMore) return;
    if (container.scrollHeight - container.scrollTop - container.clientHeight < 100) {
      setLoading(true);
      setTimeout(() => {
        setVisibleEvents((prev) => {
          const next = allEvents.slice(prev.length, prev.length + PAGE_SIZE);
          const updated = [...prev, ...next];
          setHasMore(updated.length < allEvents.length);
          setLoading(false);
          return updated;
        });
      }, 200); // simulate async
    }
  }, [loading, hasMore, allEvents]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const hasEvents = Array.isArray(visibleEvents) && visibleEvents.length > 0;

  return (
    <div ref={containerRef} className="overflow-y-auto  h-[calc(100vh-200px)] min-h-0">
      {hasEvents ? (
        <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
          {visibleEvents
            .filter((post: any) => post.category !== 'Customer')
            .map((post: any) => (
              <div key={post.id}>
                {Object.entries(themes).map(([_, innerThemeProperties]: [string, any]) => (
                  <div key={innerThemeProperties.title}>
                    {(post.category === "Storytelling"
                      ? "Literature"
                      : post.category) === innerThemeProperties.title && (
                        <Link
                        href={`/profile/amuzn/${post?.profile_name?.replace(/\s+/g, '-')}` + '?view=Events'}
                        className="w-full text-left"
                        onClick={onClose}
                        >
                          <GlobalCardEvents post={post} border={innerThemeProperties.backgroundColor} />
                          </Link>
                    )}
                  </div>
                ))}
              </div>
            ))}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="events"
            title="No events found"
            message="Try adjusting your search or filter to find events."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasEvents && loading && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasEvents && !hasMore && !loading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more data found
        </div>
      )}
      {/* Show error if any */}
      {error && (
        <div className="text-red-500 text-center py-2">{error}</div>
      )}
    </div>
  ); 
};

export default Events; 
