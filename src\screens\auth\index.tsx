"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckSquare, ChevronLeft, Square, Check, Loader } from "react-feather";
import { User } from "@/services/UserInterface"; // Import User interface
import { appleLogin, facebookLogin, googleLogin, logIn, signUp } from "@/firebase/authService";
import ForgotPass from "./forgotPass";
import { createUser, getUniqueUserName, getUserById } from "@/services/usersServices";
import { useSignInStore } from "@/components/GlobalSignInButton";
import { GetWalletId, UpdateAuthBridge_V2, UpdateOrCreateUser } from "@/services/authBridgeService";
import { UserInfo, UserProfile } from "firebase/auth";
import { doc, getDoc, Timestamp } from "firebase/firestore";
import { useAccount } from "wagmi";
import { DefaultProfileQuery } from "@/graphql/generated";
import useAuth from "@/hook";
import { initFirebase } from "../../../firebaseConfig";
// Router import removed as we're using window.location.href for full page reloads

// Add an onClose prop to allow parent components to close the modal
interface AuthSignupProps {
  onClose?: () => void;
}

const AuthSignup: React.FC<AuthSignupProps> = ({ onClose }) => {
  // Router removed as we're using window.location.href for full page reloads
  const auth = useAuth();
  const { address, isConnected } = useAccount();
  const [toggle, setToggle] = useState(false);
  const [forgotPass, setForgetPass] = useState(false);
  const [isLogin, setIsLogin] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");
  const [tnc, setTNC] = useState(false);
  const [error, setError]: any = useState("");

  // Loading and success states
  const [loginLoading, setLoginLoading] = useState(false);
  const [signupLoading, setSignupLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [appleLoading, setAppleLoading] = useState(false);
  const [facebookLoading, setFacebookLoading] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);

  // connect web2 -> web3

  const Web2Bridge = async ({ user }: { user: UserInfo }) => {
    const { address, connector, isConnected } = useAccount();

    const _email: string | null = user.email;
    const _wallet_id: string | null = address ?? (await GetWalletId());
    const _user_id: string | null = user.uid;
    const lens_user: DefaultProfileQuery["defaultProfile"] = JSON.parse(
      localStorage.getItem("lens-user") || "{}"
    );

    if (!_wallet_id || !_user_id) {
      return;
    }

    await UpdateAuthBridge_V2({
      email: _email,
      user_id: _user_id,
      wallet_id: _wallet_id,
      lens_code: lens_user?.id,
      lens_id: lens_user?.handle?.fullHandle,
    });

    // Don't reload here - we'll handle the reload after modal closing
  };

  const UpdateUserData = async (user: { password: string; uid: string; email: string }) => {
    try {
      // udpate user data

      await UpdateOrCreateUser(user.uid, {
        email: user.email,
        last_seen: Timestamp.now().toDate(),
        password: user.password,
      });

      return "success";
    } catch (error) {
      console.log({ error });
    }
  };

  const handleSignUp = async () => {
    try {
      // Reset states
      setError("");
      setSignupLoading(true);

      if (!email) {
        setError("Email required");
        setSignupLoading(false);
      } else if (!password) {
        setError("Password required");
        setSignupLoading(false);
      } else if (!(password == repeatPassword)) {
        setError("Password and Repeat Password Don't Match");
        setSignupLoading(false);
      } else if (!tnc) {
        setError("You must accept the Terms and Conditions");
        setSignupLoading(false);
      } else {
        // Register the user
        const user = await signUp(email, password);

        if (user.code) {
          setError(user.code);
          setSignupLoading(false);
          return;
        }

        // Create user profile
        await handleCreate(user.uid, user.email);

        // Store user data in localStorage
        localStorage.setItem("user", JSON.stringify(user));

        // Show success state
        setSignupLoading(false);
        setSignupSuccess(true);

        // Automatically log the user in after registration
        try {
          // Bridge web2 and web3 accounts
          await Web2Bridge({
            user: {
              uid: user.uid,
              email: user.email,
            } as UserInfo,
          });

          // Wait 1.5 seconds to show success state, then close modal and redirect
          setTimeout(() => {
            try {
              // Reset all modal states to fully close the modal
              setToggle(false);
              setLoginSuccess(false);
              setSignupSuccess(false);

              // Call the onClose prop if provided to close the parent modal
              if (onClose) {
                onClose();
              }

              // Small delay before redirecting to ensure modal is closed
              setTimeout(() => {
                // Use window.location.href instead of router.push to ensure a full page reload
                window.location.href = "/profile";
              }, 100);
            } catch (error) {
              console.error("Error during redirect:", error);
              // Reset all modal states even if there's an error
              setToggle(false);
              setLoginSuccess(false);
              setSignupSuccess(false);

              // Call the onClose prop if provided to close the parent modal
              if (onClose) {
                onClose();
              }

              window.location.href = "/profile";
            }
          }, 1500);
        } catch (error) {
          console.error("Error during auto-login:", error);
          // If auto-login fails, still close modal and redirect to profile
          setTimeout(() => {
            // Reset all modal states to fully close the modal
            setToggle(false);
            setLoginSuccess(false);
            setSignupSuccess(false);

            // Call the onClose prop if provided to close the parent modal
            if (onClose) {
              onClose();
            }

            // Small delay before redirecting
            setTimeout(() => {
              // Use window.location.href instead of router.push to ensure a full page reload
              window.location.href = "/profile";
            }, 100);
          }, 1500);
        }
      }
    } catch (error) {
      console.log(error);
      setError(error);
      setSignupLoading(false);
    }
  };

  // Handle user creation
  const handleCreate = async (userId: string, email: string | null) => {
    // check if there is lens-user in local storage
    const syncProfileFields: {
      about_me: string;
      profile_name: string;
      avatar: string;
      avatarSmall: string;
    } = {
      about_me:
        JSON.parse(localStorage?.getItem("lens-user") ?? JSON.stringify(""))?.metadata?.bio ?? "",
      profile_name:
        JSON.parse(localStorage?.getItem("lens-user") ?? JSON.stringify(""))?.metadata?.name ?? "",
      avatar:
        JSON.parse(localStorage.getItem("lens-user") ?? JSON.stringify(""))?.metadata?.picture ??
        "",
      avatarSmall:
        JSON.parse(localStorage.getItem("lens-user") ?? JSON.stringify(""))?.metadata?.picture ??
        "",
    };

    let _profile_name = await getUniqueUserName(syncProfileFields?.profile_name);
    
    const newUser: User = {
      id: userId, // Include the generated ID here
      about_me: syncProfileFields?.about_me,
      app_version: "1.0.8",
      basketOrdersCount: 0,
      bookmarks: [],
      categories: ["Customer"],
      created_at: Timestamp.now().toDate(),
      currency: "gbp",
      currentStatus: "",
      date_of_birth: "",
      device_language: "",
      email: email,
      events: [],
      full_name: "",
      hashtags: [],
      isEightyPercentsFilled: false,
      isMultiCurrenciesInfoShowed: false,
      isUS: true,
      languages: ["English"],
      last_seen: Timestamp.now().toDate(),
      location: "",
      myOpenOrdersCount: 0,
      password: "",
      // payment_method_id: null,
      personal_moto: "",
      posts: [],
      profile_name:_profile_name ,
      services: [],
      starredPosts: [],
      // stripe_id: undefined,
      type: "user",
      avatar: syncProfileFields.avatar,
      avatarSmall: syncProfileFields.avatarSmall,
      facebookLink: "",
      instagramLink: "",
      followers: [],
      following: [],
      post_bookmarked: [],
    };

    // Create the user in the database
    const response = await createUser(userId, newUser);
    // console.log({ response });

    // Return the response so the calling function can handle redirection
    return response;
  };

  const handleLogIn = async () => {
    try {
      // Reset states
      setError("");
      setLoginLoading(true);

      if (!email) {
        setError("Email required");
        setLoginLoading(false);
        return;
      } else if (!password) {
        setError("Password required");
        setLoginLoading(false);
        return;
      }

      const user = await logIn(email, password);
      localStorage.setItem("user", JSON.stringify(user)); // Store user data

      // Get user data to ensure it exists (we don't need the response for redirection)
      await getUserById(user.uid);

      // Show success state
      setLoginLoading(false);
      setLoginSuccess(true);

      // Wait 1.5 seconds to show success state, then close modal and redirect
      setTimeout(async () => {
        try {
          // First try to bridge the web2 and web3 accounts
          await Web2Bridge({ user });

          // Reset all modal states to fully close the modal
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Small delay before redirecting to ensure modal is closed
          setTimeout(() => {
            // Use window.location.href instead of router.push to ensure a full page reload
            window.location.href = "/profile";
          }, 100);
        } catch (error) {
          console.error("Error during redirect:", error);
          // Reset all modal states even if there's an error
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Fallback to traditional redirect if router fails
          setTimeout(() => {
            window.location.href = "/profile";
          }, 100);
        }
      }, 1500);
    } catch (error) {
      console.error("Error logging in:", error);
      setError("Invalid email or password. Please try again.");
      setLoginLoading(false);
    }
  };

  // google login
  const handleGoogleLogin = async () => {
    try {
      // Reset states
      setError("");
      setGoogleLoading(true);

      const user = await googleLogin();
      localStorage.setItem("user", JSON.stringify(user)); // Store user data

      const { db } = await initFirebase();
      const userRef = doc(db, "users", user.uid);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        await handleCreate(user.uid, user.email);
        await UpdateOrCreateUser(user.uid, {
          email: user.email,
          last_seen: Timestamp.now().toDate(),
          password: null,
        });
      }

      await Web2Bridge({ user });

      // Show success state
      setGoogleLoading(false);
      setLoginSuccess(true);

      // Wait 1.5 seconds to show success state, then close modal and redirect
      setTimeout(async () => {
        try {
          // Reset all modal states to fully close the modal
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Small delay before redirecting to ensure modal is closed
          setTimeout(() => {
            // Use window.location.href instead of router.push to ensure a full page reload
            window.location.href = "/profile";
          }, 100);
        } catch (error) {
          console.error("Error during redirect:", error);
          // Reset all modal states even if there's an error
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Fallback to traditional redirect
          setTimeout(() => {
            window.location.href = "/profile";
          }, 100);
        }
      }, 1500);
    } catch (error: any) {
      console.error("Google Login Failed:", error.message);
      setError("Google login failed. Please try again.");
      setGoogleLoading(false);
    }
  };

  // apple login
  const handleAppleLogin = async () => {
    try {
      // Reset states
      setError("");
      setAppleLoading(true);

      const user = await appleLogin();
      localStorage.setItem("user", JSON.stringify(user)); // Store user data

      const { db } = await initFirebase();
      const userRef = doc(db, "users", user.uid);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        await handleCreate(user.uid, user.email);
        await UpdateOrCreateUser(user.uid, {
          email: user.email,
          last_seen: Timestamp.now().toDate(),
          password: null,
        });
      }

      await Web2Bridge({ user });

      // Show success state
      setAppleLoading(false);
      setLoginSuccess(true);

      // Wait 1.5 seconds to show success state, then close modal and redirect
      setTimeout(async () => {
        try {
          // Reset all modal states to fully close the modal
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Small delay before redirecting to ensure modal is closed
          setTimeout(() => {
            // Use window.location.href instead of router.push to ensure a full page reload
            window.location.href = "/profile";
          }, 100);
        } catch (error) {
          console.error("Error during redirect:", error);
          // Reset all modal states even if there's an error
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Fallback to traditional redirect
          setTimeout(() => {
            window.location.href = "/profile";
          }, 100);
        }
      }, 1500);
    } catch (error) {
      console.error("Apple login failed", error);
      setError("Apple login failed. Please try again.");
      setAppleLoading(false);
    }
  };

  // facebook login
  const handleFacebookLogin = async () => {
    try {
      // Reset states
      setError("");
      setFacebookLoading(true);

      const user = await facebookLogin();
      localStorage.setItem("user", JSON.stringify(user)); // Store user data

      const { db } = await initFirebase();
      const userRef = doc(db, "users", user.uid);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        await handleCreate(user.uid, user.email);
        await UpdateOrCreateUser(user.uid, {
          email: user.email,
          last_seen: Timestamp.now().toDate(),
          password: null,
        });
      }

      await Web2Bridge({ user });

      // Show success state
      setFacebookLoading(false);
      setLoginSuccess(true);

      // Wait 1.5 seconds to show success state, then close modal and redirect
      setTimeout(async () => {
        try {
          // Reset all modal states to fully close the modal
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Small delay before redirecting to ensure modal is closed
          setTimeout(() => {
            // Use window.location.href instead of router.push to ensure a full page reload
            window.location.href = "/profile";
          }, 100);
        } catch (error) {
          console.error("Error during redirect:", error);
          // Reset all modal states even if there's an error
          setToggle(false);
          setLoginSuccess(false);
          setSignupSuccess(false);

          // Call the onClose prop if provided to close the parent modal
          if (onClose) {
            onClose();
          }

          // Fallback to traditional redirect
          setTimeout(() => {
            window.location.href = "/profile";
          }, 100);
        }
      }, 1500);
    } catch (error) {
      console.error("Facebook login failed:", error);
      setError("Facebook login failed. Please try again.");
      setFacebookLoading(false);
    }
  };
  return (
    <>
      {!forgotPass ? (
        <>
          {!toggle ? (
            <div className="text-center">
              <div className="flex flex-row items-center justify-center mb-4">
                <img
                  src="/assets/logo-no-color.svg"
                  alt=""
                  height={120}
                  className="items-center h-[120px]"
                />
              </div>

              <p className="text-center text-primary font-[600] text-2xl mb-3">Let's start</p>
              <div className="flex flex-row items-center gap-3">
                <Button
                  className=" rounded-full w-full text-sm bg-primary text-white"
                  onClick={() => {
                    setToggle(true), setIsLogin(false);
                  }}
                >
                  Register
                </Button>
                <Button
                  className=" rounded-full w-full text-sm bg-primary text-white "
                  onClick={() => {
                    setToggle(true), setIsLogin(true);
                  }}
                >
                  Login
                </Button>
              </div>
              <p className="text-center mb-5 mt-3 text-subtitle  text-lg">or</p>

              <Button
                className="rounded-full w-full border-borderColor border-2 text-base text-black py-2 flex items-center justify-center gap-2"
                variant="outline"
                onClick={handleGoogleLogin}
                disabled={googleLoading}
              >
                {googleLoading ? (
                  <>
                    <Loader size={18} className="animate-spin" />
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <img src="/assets/google.svg" alt="" height={20} className="h-[20px]" />
                    Continue with Google
                  </>
                )}
              </Button>
              <Button
                className="rounded-full w-full border-borderColor border-2 text-base text-black mt-5 py-2 flex items-center justify-center gap-2"
                variant="outline"
                onClick={handleAppleLogin}
                disabled={appleLoading}
              >
                {appleLoading ? (
                  <>
                    <Loader size={18} className="animate-spin" />
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <img src="/assets/apple.svg" alt="" height={20} className="h-[20px]" />
                    Continue with Apple
                  </>
                )}
              </Button>
              {/* <Button
                className="rounded-full w-full border-borderColor border-2 text-base text-black mt-5 py-2 flex items-center justify-center gap-2"
                variant="outline"
                onClick={handleFacebookLogin}
                disabled={facebookLoading}
              >
                {facebookLoading ? (
                  <>
                    <Loader size={18} className="animate-spin" />
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <img src="/assets/facebook.svg" alt="" height={20} className="h-[20px]" />
                    Continue with Facebook
                  </>
                )}
              </Button> */}
              {(!address || !isConnected) && (
                <div>
                  <p className="text-center mb-5 mt-3 text-subtitle text-lg">or</p>
                  <Button
                    className="rounded-full w-full border-borderColor border-2 text-base text-black py-2 flex items-center justify-center gap-2"
                    variant="outline"
                    onClick={() => {
                      // Close the current modal if onClose is provided
                      if (onClose) {
                        onClose();
                      }
                      // Open the global SignInButton
                      useSignInStore.getState().setIsOpen(true);
                    }}
                  >
                    <img src="/assets/wallet.svg" alt="" height={20} className="h-[20px]" />
                    Connect Lens Account
                  </Button>
                </div>
              )}
              <p className="text-center mb-5 mt-3 text-subtitle  text-lg">or</p>
              <p className="text-center font-bold">Browse as guest</p>

              <p className="mt-5 text-borderColor">By proceeding you agree</p>
              <p className="text-borderColor">
                to the{" "}
                <a href="#" className="border-b-2 border-subtitle text-subtitle font-bold">
                  terms and conditions.
                </a>{" "}
              </p>
            </div>
          ) : isLogin ? (
            <div className="flex flex-col justify-center items-center w-full h-full px-4 md:px-6">
              {loginSuccess ? (
                <div className="flex flex-col items-center justify-center py-10 w-full max-w-md">
                  <div className="bg-green-100 rounded-full p-4 mb-4">
                    <Check size={48} className="text-green-500" />
                  </div>
                  <h2 className="text-xl font-bold mb-2 text-center">Login Successful!</h2>
                  <p className="text-gray-500 text-center mb-4">
                    You have successfully logged in. Redirecting you to your profile...
                  </p>
                </div>
              ) : (
                <div className="w-full max-w-md">
                  <div
                    className="flex items-center gap-2 absolute top-6 left-6 cursor-pointer"
                    onClick={() => setToggle(false)}
                  >
                    <ChevronLeft size={20} />
                    <p className="text-primary">Back</p>
                  </div>

                  <div className="flex flex-col items-center justify-center mb-6 max-md:hidden">
                    <img
                      src="/assets/logo-no-color.svg"
                      alt=""
                      height={120}
                      className="h-[120px]"
                    />
                  </div>

                  <h2 className="text-center text-primary font-[600] text-2xl mb-5">Login</h2>

                  {error && (
                    <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4 text-sm">
                      {error}
                    </div>
                  )}

                  <div className="space-y-5 w-full">
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="email" className="text-titleLabel font-bold text-lg">
                        Email
                      </Label>
                      <Input
                        type="email"
                        id="email"
                        placeholder="Email"
                        className="text-primary w-full"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loginLoading}
                      />
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="password" className="text-titleLabel font-bold text-lg">
                        Password
                      </Label>
                      <Input
                        type="password"
                        id="password"
                        placeholder="Password"
                        className="text-primary w-full"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={loginLoading}
                      />
                    </div>
                  </div>

                  <div
                    className="text-center mt-6 mb-5 cursor-pointer"
                    onClick={() => {
                      setForgetPass(true);
                      setIsLogin(false);
                      setToggle(false);
                    }}
                  >
                    <a href="#" className="text-center font-[600] text-primary">
                      Forgot password?
                    </a>
                  </div>

                  <Button
                    className="rounded-full w-full border-primary btn border-2 text-primary py-2 hover:bg-primary hover:text-white flex items-center justify-center h-12"
                    variant="outline"
                    onClick={handleLogIn}
                    disabled={loginLoading}
                  >
                    {loginLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader size={18} className="animate-spin" />
                        <span>Signing In...</span>
                      </div>
                    ) : (
                      "Sign In"
                    )}
                  </Button>

                  <p className="text-borderColor mt-5 text-center">
                    Don't have an account?{" "}
                    <a
                      href="#"
                      className="text-primary font-bold"
                      onClick={() => {
                        setIsLogin(false);
                        setError("");
                      }}
                    >
                      Register
                    </a>
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col justify-center items-center w-full h-full px-4 md:px-6">
              {signupSuccess ? (
                <div className="flex flex-col items-center justify-center py-10 w-full max-w-md">
                  <div className="bg-green-100 rounded-full p-4 mb-4">
                    <Check size={48} className="text-green-500" />
                  </div>
                  <h2 className="text-xl font-bold mb-2 text-center">Registration Successful!</h2>
                  <p className="text-gray-500 text-center mb-4">
                    Your account has been created successfully. You are now being logged in
                    automatically. Redirecting you to your profile...
                  </p>
                </div>
              ) : (
                <div className="w-full max-w-md">
                  <div
                    className="flex items-center gap-2 absolute top-6 left-6 cursor-pointer"
                    onClick={() => setToggle(false)}
                  >
                    <ChevronLeft size={20} />
                    <p className="text-primary">Back</p>
                  </div>

                  <div className="flex flex-col items-center justify-center mb-6 max-md:hidden">
                    <img
                      src="/assets/logo-no-color.svg"
                      alt=""
                      height={120}
                      className="h-[120px]"
                    />
                  </div>

                  <h2 className="text-center text-primary font-[600] text-2xl mb-5">Register</h2>

                  {error && (
                    <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4 text-sm">
                      {error}
                    </div>
                  )}

                  <div className="space-y-5 w-full">
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="email" className="text-titleLabel font-bold text-lg">
                        Email
                      </Label>
                      <Input
                        type="email"
                        id="email"
                        placeholder="Email"
                        className="text-primary w-full"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={signupLoading}
                      />
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="password" className="text-titleLabel font-bold text-lg">
                        Password
                      </Label>
                      <Input
                        type="password"
                        id="password"
                        placeholder="Password"
                        className="text-primary w-full"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={signupLoading}
                      />
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label
                        htmlFor="password-repeat"
                        className="text-titleLabel font-bold text-lg"
                      >
                        Repeat password
                      </Label>
                      <Input
                        type="password"
                        id="password-repeat"
                        placeholder="Password"
                        className="text-primary w-full"
                        value={repeatPassword}
                        onChange={(e) => setRepeatPassword(e.target.value)}
                        disabled={signupLoading}
                      />
                    </div>
                  </div>

                  <div
                    onClick={() => setTNC(!tnc)}
                    className="flex items-center gap-2 mt-6 mb-5 cursor-pointer"
                  >
                    {!tnc ? <Square size={16} /> : <CheckSquare size={16} />}

                    <div className="text-sm">
                      I agree with{" "}
                      <a href="#" className="font-[600] text-primary">
                        Terms and Conditions
                      </a>
                    </div>
                  </div>

                  <Button
                    className="rounded-full w-full border-primary btn border-2 text-primary py-2 hover:bg-primary hover:text-white flex items-center justify-center h-12"
                    variant="outline"
                    onClick={handleSignUp}
                    disabled={signupLoading}
                  >
                    {signupLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader size={18} className="animate-spin" />
                        <span>Creating Account...</span>
                      </div>
                    ) : (
                      "Create account"
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </>
      ) : (
        forgotPass && (
          <div className="flex flex-col justify-center w-full items-center h-full px-4 md:px-6">
            <div className="w-full max-w-md">
              <div
                className="flex items-center gap-2 absolute top-6 left-6 cursor-pointer"
                onClick={() => {
                  setForgetPass(false);
                  setIsLogin(false);
                }}
              >
                <ChevronLeft size={20} />
                <p className="text-primary">Back</p>
              </div>
              <div>
                <ForgotPass />
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
};

export default AuthSignup;
