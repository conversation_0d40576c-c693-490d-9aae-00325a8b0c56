import React, { useEffect, useState, useRef, useCallback } from "react";
import { FilterSearchManager, PostSearchBy } from "@/services/filtersServices";
import EmptyState from "@/components/EmptyState";
import LazyMedia from "../LazyMedia";
import { themes } from "../../../theme";
import Link from "next/link";

const Posts = ({ searchFor, searchBy, search, SEARCH_FOR, SEARCH_BY_MAP, onClose, filters }: any) => {
  const [posts, setPosts] = useState<any[]>([]); // loaded posts
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextStartAfterAddedAt, setNextStartAfterAddedAt] = useState<any>(undefined);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const PAGE_SIZE = 50;

  // Fetch posts (initial or paginated)
  const fetchPosts = useCallback(async (isInitial = false) => {
    let filterBy;
    if (searchBy === 'Description' || searchBy === 'About Project' || searchBy === 'Title') {
      filterBy = PostSearchBy.ABOUT_PROJECT;
    } else if (searchBy === 'Hashtag') {
      filterBy = PostSearchBy.HASHTAGS;
    } else if (searchBy === 'Location' || searchBy === 'Geotags') {
      filterBy = PostSearchBy.GEOTAGS;
    } else {
      setPosts([]);
      setHasMore(false);
      setNextStartAfterAddedAt(undefined);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const resp = await FilterSearchManager.getInstance().GetPostByFilters({
        payload: {
          filterBy,
          searchTerm: search,
          limit: PAGE_SIZE,
          startAfterAddedAt: isInitial ? undefined : nextStartAfterAddedAt,
          filters,
        }
      });
      if (isInitial) {
        setPosts(resp.posts || []);
      } else {
        setPosts(prev => [...prev, ...(resp.posts || [])]);
      }
      setNextStartAfterAddedAt(resp.nextStartAfterAddedAt);
      setHasMore(resp.hasMore);
    } catch (error: any) {
      setError(error && error.message ? error.message : 'Error fetching posts');
    } finally {
      setLoading(false);
    }
  }, [search, searchBy, nextStartAfterAddedAt, filters]);

  // Initial fetch or when search/searchBy changes
  useEffect(() => {
    setPosts([]);
    setNextStartAfterAddedAt(undefined);
    setHasMore(true);
    fetchPosts(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, searchBy, filters]);

  // Scroll handler for backend pagination
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || loading || !hasMore) return;
    if (container.scrollHeight - container.scrollTop - container.clientHeight < 100) {
      fetchPosts(false);
    }
  }, [loading, hasMore, fetchPosts]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const hasPosts = Array.isArray(posts) && posts.length > 0;

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;
    if (!postFile) {
      return undefined;
    }
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }
    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  // Custom wrapper to handle fallback image or video on error
  const PostMedia = ({ src, alt, border, type, mediaType }: { src: string | undefined; alt: string; border: string; type: string, mediaType: string }) => {
    const [hasError, setHasError] = useState(false);
    return (
      <div>
        {mediaType == 'video' ? (
          <LazyMedia
            src={src}
            alt={alt}
            type='video'
            className="h-[125px] w-[125px] object-cover border-3"
            style={{ borderColor: border }}
            placeholderClassName="bg-gray-100"
            onError={() => setHasError(true)}
            controls={false}
            autoPlay={false}
            muted={true}
            showPlayIcon={true}
            enableLightbox={true}
          />
        ) : (
          <LazyMedia
            src={src}
            alt={alt}
            type='image'
            className="h-[125px] w-[125px] object-cover border-3"
            style={{ borderColor: border }}
            placeholderClassName="bg-gray-100"
            onError={() => setHasError(true)}
          />
        )}
      </div>
    );
  };

  return (
    <div ref={containerRef} className="overflow-y-auto h-[calc(100vh-200px)] min-h-0">
      {hasPosts ? (
        <div className="flex flex-wrap gap-1 mt-2">
          {posts
            .filter((post: any) => post.category !== 'Customer')
            .map((post: any) => {
              const matchedTheme = Object.entries(themes).find(([_key, theme]: [string, any]) =>
                (post.category === "Storytelling" ? "Literature" : post.category) === theme.title
              );
              if (!matchedTheme) return null;
              const imgSrc = generateFileUrl(post.postFile);
              return (
                <div key={post.id} className="relative flex flex-col items-center mb-0" style={{ width: 125, height: 125 }}>
                  <div className="relative" style={{ width: 125, height: 125 }}>
                    <Link
                      href={`/browse/${post?.category}/${post?.id}%20${post.user_id}`}
                      className="w-full text-left"
                      onClick={onClose}
                    >
                      <PostMedia src={imgSrc} mediaType={post.mediaType} alt={`Post ${post.id}`} border={matchedTheme[1].backgroundColor} type={post.mediaType} />
                    </Link>
                  </div>
                </div>
              );
            })}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="posts"
            title="No posts found"
            message="Try adjusting your search or filter to find posts."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasPosts && loading && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasPosts && !hasMore && !loading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more data found
        </div>
      )}
      {/* Show error if any */}
      {error && (
        <div className="text-red-500 text-center py-2">{error}</div>
      )}
    </div>
  );
};

export default Posts; 