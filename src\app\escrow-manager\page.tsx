'use client';

import { useState } from 'react';

export default function EscrowManager() {
  const [orderId, setOrderId] = useState('order_15');
  const [chargeId, setChargeId] = useState('ch_3RjhR4E85bVPMZKN0LHt2Xha'); // Latest charge from your payment
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState('');

  const handleStateChange = async (newState: string) => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/escrow/manage-state', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          chargeId,
          newState,
          metadata: {
            processedAt: new Date().toISOString(),
            source: 'escrow-manager-ui'
          }
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to process state change');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            🔒 Escrow State Manager
          </h1>

          {/* Input Section */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-800 mb-4">Transaction Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Order ID</label>
                <input
                  type="text"
                  value={orderId}
                  onChange={(e) => setOrderId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="order_15"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Charge ID</label>
                <input
                  type="text"
                  value={chargeId}
                  onChange={(e) => setChargeId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  placeholder="ch_..."
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <button
              onClick={() => handleStateChange('accept')}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-4 px-6 rounded-lg transition-colors"
            >
              {loading ? '⏳ Processing...' : '✅ ACCEPT (10%)'}
            </button>
            <button
              onClick={() => handleStateChange('delivered')}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-4 px-6 rounded-lg transition-colors"
            >
              {loading ? '⏳ Processing...' : '🚚 DELIVERED (10%)'}
            </button>
            <button
              onClick={() => handleStateChange('completed')}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-4 px-6 rounded-lg transition-colors"
            >
              {loading ? '⏳ Processing...' : '🎉 COMPLETED (80%)'}
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Success Result */}
          {result && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-4">✅ {result.message}</h3>
              
              {result.stripeData && (
                <div className="space-y-4">
                  {/* Charge Information */}
                  <div className="bg-white p-4 rounded border">
                    <h4 className="font-semibold text-gray-900 mb-2">💳 Charge Details</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Captured Amount:</span>
                        <p className="font-semibold">{formatAmount(result.stripeData.capturedAmount)}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Transferred to Seller:</span>
                        <p className="font-semibold text-green-600">{formatAmount(result.stripeData.transferredAmount)}</p>
                      </div>
                      {result.stripeData.remainingCapturable !== undefined && (
                        <div>
                          <span className="text-gray-600">Remaining Capturable:</span>
                          <p className="font-semibold text-orange-600">{formatAmount(result.stripeData.remainingCapturable)}</p>
                        </div>
                      )}
                      {result.stripeData.totalCaptured && (
                        <div>
                          <span className="text-gray-600">Total Captured:</span>
                          <p className="font-semibold">{formatAmount(result.stripeData.totalCaptured)}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Transfer Information */}
                  {result.stripeData.transfer && (
                    <div className="bg-white p-4 rounded border">
                      <h4 className="font-semibold text-gray-900 mb-2">💸 Transfer Details</h4>
                      <div className="text-sm">
                        <p><span className="text-gray-600">Transfer ID:</span> <span className="font-mono">{result.stripeData.transfer.id}</span></p>
                        <p><span className="text-gray-600">Amount:</span> <span className="font-semibold">{formatAmount(result.stripeData.transfer.amount)}</span></p>
                        <p><span className="text-gray-600">Destination:</span> <span className="font-mono">{result.stripeData.transfer.destination}</span></p>
                      </div>
                    </div>
                  )}

                  {result.stripeData.escrowCompleted && (
                    <div className="bg-purple-100 p-4 rounded border-2 border-purple-300">
                      <h4 className="font-bold text-purple-800">🎉 Escrow Completed!</h4>
                      <p className="text-purple-700 text-sm mt-1">All funds have been released to the seller.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📋 How It Works</h3>
            <div className="space-y-3 text-sm text-gray-700">
              <div className="flex items-start">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">ACCEPT</span>
                <p>Captures 10% of payment and transfers 84% of that to seller (8.4% of total)</p>
              </div>
              <div className="flex items-start">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">DELIVERED</span>
                <p>Captures another 10% and transfers 84% of that to seller (8.4% more of total)</p>
              </div>
              <div className="flex items-start">
                <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">COMPLETED</span>
                <p>Captures remaining 80% and transfers 84% of that to seller (67.2% of total)</p>
              </div>
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 text-xs">
                  <strong>Total to Seller:</strong> 84% | <strong>Platform Fee:</strong> 16%
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
