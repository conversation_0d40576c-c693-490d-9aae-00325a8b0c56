import React, { useState } from "react";
import { X, Check } from "react-feather";

const Category = [
  // "My Feed",
  "Music",
  "Literature",
  "Art",
  "Theatre & Performance",
  "Film & Photography",
  "Multidisciplinary",
  "Groups",
];

const Dateofpublishing = ["Past 24 hours ", "Past week", "Past month"];
const ProfileName = ["Tom Li", "Kim Li 2"];
const Postlocation = ["United Kingdom", "United States", "United States 2"];

export default function SidebarFilter({
  onOpenChange,
  onApply,
  filters = { category: [], profileNames: [], locations: [], dates: [] },
}: {
  onOpenChange: () => void;
  onApply: (filters: {
    category: string[];
    profileNames: string[];
    locations: string[];
    dates: string[];
  }) => void;
  filters?: {
    category: string[];
    profileNames: string[];
    locations: string[];
    dates: string[];
  };
}) {
  const [activeSheet, setActiveSheet] = useState(1);
  const showSheet = (sheetNumber: number) => setActiveSheet(sheetNumber);

  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectedProfileNames, setSelectedProfileNames] = useState<number[]>([]);
  const [selectedPostLocations, setSelectedPostLocations] = useState<number[]>([]);
  const [selectedDates, setSelectedDates] = useState<number[]>([]);

  // Sync local state with parent filters prop
  React.useEffect(() => {
    // Helper to map filter values to indices
    const getIndices = (values: string[], options: string[]) =>
      values.map((val) => options.indexOf(val)).filter((i) => i !== -1);
    setSelectedCategories(getIndices(filters.category || [], Category));
    setSelectedProfileNames(getIndices(filters.profileNames || [], ProfileName));
    setSelectedPostLocations(getIndices(filters.locations || [], Postlocation));
    setSelectedDates(getIndices(filters.dates || [], Dateofpublishing));
  }, [filters]);

  const toggleCategory = (index: number) => {
    setSelectedCategories((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const toggleProfileName = (index: number) => {
    setSelectedProfileNames((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const togglePostLocation = (index: number) => {
    setSelectedPostLocations((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const toggleDate = (index: number) => {
    setSelectedDates((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  return (
    <div className=" flex items-center justify-center ">
      <div className="bg-white w-[22rem] max-md:w-full rounded-lg  relative">
        {activeSheet === 1 && (
          <>
            <div>
              <div className="flex justify-between items-center border-b px-4 py-3">
                <button className="text-gray-500" onClick={() => onOpenChange()}><X /></button>
                <p className="text-lg font-bold text-titleLabel">Filter Posts</p>
                <button
                  className={`text-lg font-bold ${selectedCategories.length > 0 || selectedProfileNames.length > 0 || selectedPostLocations.length > 0 || selectedDates.length > 0
                      ? 'text-primary'
                      : 'text-borderColor'
                    }`}
                  onClick={() => {
                    setSelectedCategories([]);
                    setSelectedProfileNames([]);
                    setSelectedPostLocations([]);
                    setSelectedDates([]);
                    onApply({
                      category: [],
                      profileNames: [],
                      locations: [],
                      dates: [],
                    });
                    // onOpenChange();
                  }}
                >
                  Clear
                </button>
              </div>
              <div className="p-4">
                <div className="filter-list cursor-pointer flex justify-between items-center mb-4" onClick={() => showSheet(2)}>
                  <p className="text-titleLabel">Category</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div className="filter-list cursor-pointer flex justify-between items-center mb-4" onClick={() => showSheet(3)}>
                  <p className="text-titleLabel">Date of publishing</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div className="filter-list cursor-pointer flex justify-between items-center mb-4" onClick={() => showSheet(4)}>
                  <p className="text-titleLabel">Post location</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
                <div className="filter-list cursor-pointer flex justify-between items-center" onClick={() => showSheet(5)}>
                  <p className="text-titleLabel">Profile name</p>
                  <div className="flex gap-3 items-center">
                    <p className="text-borderColor">Any</p>
                    <span>&#8250;</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 w-full p-4 bg-white border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => {
                  onApply({
                    category: selectedCategories.map((i) => Category[i]),
                    profileNames: selectedProfileNames.map((i) => ProfileName[i]),
                    locations: selectedPostLocations.map((i) => Postlocation[i]),
                    dates: selectedDates.map((i) => Dateofpublishing[i]),
                  });
                 
                }}
              >
                Apply
              </button>
            </div>
          </>
        )}
        {activeSheet === 2 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button className="flex items-center gap-2 text-gray-500" onClick={() => showSheet(1)}>&#8249; Back</button>
              <p className="text-base font-bold text-titleLabel">Category</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {Category.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleCategory(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedCategories.includes(index) && <Check color='#25282B' strokeWidth='1.6px' />}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 3 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button className="flex items-center gap-2 text-gray-500" onClick={() => showSheet(1)}>&#8249; Back</button>
              <p className="text-base font-bold text-titleLabel">Date of publishing</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {Dateofpublishing.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleDate(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedDates.includes(index) && <Check color='#25282B' strokeWidth='1.6px' />}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 4 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button className="flex items-center gap-2 text-gray-500" onClick={() => showSheet(1)}>&#8249; Back</button>
              <p className="text-base font-bold text-titleLabel">Post location</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {Postlocation.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => togglePostLocation(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedPostLocations.includes(index) && <Check color='#25282B' strokeWidth='1.6px' />}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
        {activeSheet === 5 && (
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center border-b px-4 py-3">
              <button className="flex items-center gap-2 text-gray-500" onClick={() => showSheet(1)}>&#8249; Back</button>
              <p className="text-base font-bold text-titleLabel">Profile name</p>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {ProfileName.map((item, index) => (
                <div
                  key={index}
                  className="filter-list flex justify-between items-center mb-4 cursor-pointer"
                  onClick={() => toggleProfileName(index)}
                >
                  <p className="text-titleLabel">{item}</p>
                  {selectedProfileNames.includes(index) && <Check color='#25282B' strokeWidth='1.6px' />}
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <button
                className="w-full bg-primary text-white py-2 rounded"
                onClick={() => showSheet(1)}
              >
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 