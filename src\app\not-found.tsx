"use client";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4">
      <div className="max-w-md w-full text-center">
        <h1 className="text-7xl font-extrabold text-primary mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">Page Not Found</h2>
        <p className="text-gray-500 mb-8">
          Sorry, the page you are looking for does not exist or has been moved.
        </p>
        <Link
          href="/"
          className="inline-block px-6 py-3 bg-primary text-white rounded-full font-semibold shadow transition"
        >
          Go Home
        </Link>
      </div>
      <div className="absolute bottom-8 right-8 text-xs text-gray-300 select-none">
        &copy; {new Date().getFullYear()} Amuzn. All rights reserved.
      </div>
    </div>
  );
} 