// const functions = require("firebase-functions");
// // import * as functions from 'firebase-functions';

// const firestore = require("@google-cloud/firestore");
// // import { Storage } from "@google-cloud/storage";
// const { Storage } = require("@google-cloud/storage");

// /**
//  *
//  *  Backup firestore
//  *
//  */

// const client = new firestore.v1.FirestoreAdminClient();

// // Replace BUCKET_NAME
// const bucketName = "amuzn-webapp-dev-backup";

// const bucket = `gs://${bucketName}`;
// const storage = new Storage();

// exports.scheduledFirestoreExport = functions.scheduler
//   .schedule("every 2 minutes")
//   // .schedule("every 24 hours")
//   .onRun(async (context: any) => {
//     try {
//       const projectId = process.env.GCP_PROJECT;
//       const databaseName = client.databasePath(projectId, "(default)");

//       const [response] = await client.exportDocuments({
//         name: databaseName,
//         outputUriPrefix: bucket,
//         collectionIds: [],
//       });
//       const operationName = response.name;
//       console.log(`Export operation: ${operationName}`);

//       // ✅ Clean up old backups — only keep the latest export folder
//       ////////////////////////////////////////////////////////////////////////////////////////////////
//       const [files] = await storage.bucket(bucketName).getFiles({ autoPaginate: false });
//       const prefixes = new Set<string>();

//       for (const file of files) {
//         const prefix = file.name.split("/")[0]; // top-level folder
//         if (prefix) prefixes.add(prefix);
//       }

//       const sorted = Array.from(prefixes).sort().reverse(); // most recent first
//       const toDelete = sorted.slice(1); // all but newest

//       for (const folder of toDelete) {
//         console.log(`Deleting old backup folder: ${folder}`);
//         await storage.bucket(bucketName).deleteFiles({ prefix: `${folder}/` });
//       }
//       ////////////////////////////////////////////////////////////////////////////////////////////////

//       return null;
//     } catch (error) {
//       console.error(error);
//       throw new Error("Export operation failed");
//     }
//   });

// const functions = require('firebase-functions');
// const firestore = require('@google-cloud/firestore');
// const client = new firestore.v1.FirestoreAdminClient();

// // Replace BUCKET_NAME
// const bucketName = "amuzn-webapp-dev-backup";

// const bucket = `gs://${bucketName}`;
// // const bucket = 'gs://BUCKET_NAME';

// exports.scheduledFirestoreExport = functions.pubsub
//                                             .schedule('every 24 hours')
//                                             .onRun(() => {

//   const projectId = process.env.GCP_PROJECT;
//   const databaseName =
//     client.databasePath(projectId, '(default)');

//   return client.exportDocuments({
//     name: databaseName,
//     outputUriPrefix: bucket,
//     // Leave collectionIds empty to export all collections
//     // or set to a list of collection IDs to export,
//     // collectionIds: ['users', 'posts']
//     collectionIds: []
//     })
//   .then((responses: any[]) => {
//     const response = responses[0];
//     console.log(`Operation Name: ${response['name']}`);
//   })
//   .catch((err: any) => {
//     console.error(err);
//     throw new Error('Export operation failed');
//   });
// });

const { onSchedule } = require("firebase-functions/v2/scheduler");
const { FirestoreAdminClient } = require("@google-cloud/firestore").v1;
const { Storage } = require("@google-cloud/storage");

const client = new FirestoreAdminClient();

// const bucketName = "amuzn-webapp-dev-backup";
const bucketName = "amuzn-webapp-backup";
const bucket = `gs://${bucketName}`;
const storage = new Storage();

exports.scheduledFirestoreExport = onSchedule(
  // "every 5 minutes",
  "0 12 * * 0", // Every Sunday at 12:00 PM
  // { region: "us" },
  async () => {
    const projectId = process.env.GCP_PROJECT || process.env.GCLOUD_PROJECT;

    const databaseName = client.databasePath(projectId, "(default)");

    try {
      const [response] = await client.exportDocuments({
        name: databaseName,
        outputUriPrefix: bucket,
        collectionIds: [],
      });
      console.log(`Started export operation: ${response?.name}`);

      // Cleanup old backups logic here...
      //       // ✅ Clean up old backups — only keep the latest export folder
      //       ////////////////////////////////////////////////////////////////////////////////////////////////

      const [files] = await storage.bucket(bucketName).getFiles({ autoPaginate: false });

      // Collect unique top-level folders (no Set)
      const folderNames = new Map<string, boolean>();

      for (const file of files) {
        const folder = file.name.split("/")[0];
        if (folder) folderNames.set(folder, true);
      }

      // Sort lexicographically, most recent first
      const sorted = Array.from(folderNames.keys()).sort().reverse();

      // Keep only the latest
      // const toDelete = sorted.slice(1);
      console.log({ sorted });

      for (const folder of sorted) {
        console.log(`Deleting old backup folder: ${folder}`);
        await storage.bucket(bucketName).deleteFiles({ prefix: `${folder}/` });
      }

      //       ////////////////////////////////////////////////////////////////////////////////////////////////
    } catch (error: any) {
      if (error) {
        try {
          console.error("Export failed:", JSON.stringify(error));
        } catch {
          console.error("Export failed:", error.message || error);
        }
      } else {
        console.error("Export failed: Unknown error");
      }
      throw new Error("Export operation failed");
    }
  }
);

// command for importing backup
// gcloud firestore import gs://<bucket-name>/<time-stamp> --async
